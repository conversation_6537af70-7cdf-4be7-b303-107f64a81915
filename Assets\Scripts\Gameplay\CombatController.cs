using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem.LowLevel;

public class CombatController : MonoBehaviour
{
    // 这个类用于实现战斗控制器的逻辑
    // 包括战斗开始、结束、玩家操作对游戏的影响
    private static CombatController _instance; // 单例模式，确保每个场景只有一个控制器实例
    public static CombatController Instance
    {
        get { return _instance; }
    }

    private List<float> _beatPoints; // 存储节拍点的列表，用于计算每次节拍结算的时间
    private float _scenceTime; // 记录场景时间
    private float _maxmumTolerance = 0.2f; // 最大容差值，用于判断玩家是否在节拍点附近
    private bool _isPlayerInput = false; // 玩家是否已经输入


    // Start is called before the first frame update
    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
        _scenceTime = 0f; // 初始化场景时间
    }
    void Start()
    {
        _beatPoints = ScenceController.Instance.GetBeatPoints(); // 获取节拍点列表
    }

    void OnEnable()
    {
        // 订阅玩家动作事件
        PlayerActions.OnPlayerInput += HandlePlayerAction;
    }


    // Update is called once per frame
    void FixedUpdate()
    {
        // 在这里实现战斗逻辑
        _scenceTime += Time.deltaTime; // 更新场景时间
        if (_beatPoints.Count == 0) return; // 如果没有节拍点，直接返回
        if (_isPlayerInput) // 如果玩家已经输入
        {
            if (CanSettlement())  //如果进入结算时间
            {
                TriggerSettlement();
            }
        }
        else if (MissBeatPoints()) // 如果玩家没有输入，检查是否错过了节拍点
        {
            TriggerSettlement();
        }
    }

    // 检查是否接近节拍点
    private bool MissBeatPoints()
    {
        //检查是否错过了节拍点
        if (_beatPoints.Count == 0) return false; // 如果没有节拍点，直接返回
        float currentBeatPoint = _beatPoints[0]; // 获取当前节拍点
        if (_scenceTime - currentBeatPoint > _maxmumTolerance) // 检查是否在容差范围内
        {
            // 如果不在容差范围内，说明玩家错过了节拍
            return true;
        }
        return false; // 否则返回false
    }

    private bool CanSettlement()
    {
        //检查是否可以结算
        if (_beatPoints.Count == 0) return false; // 如果没有节拍点，直接返回
        if (_scenceTime >= _beatPoints[0]) return true; // 如果场景时间大于等于节拍点，说明可以结算
        return false;
    }

    private void TriggerSettlement()
    {
        //触发结算
        _beatPoints.RemoveAt(0); // 移除第一个节拍点
        _isPlayerInput = false; // 重置玩家输入状态
        //执行行动
        EnemyAction enemyAction = EnemyController.Instance.GetNextAction();
    }

    #region Player Actions
    private void HandlePlayerAction(PlayerAction playerAction)
    {
        // 判定玩家输入时机
        if (_beatPoints.Count == 0) return; // 如果没有节拍点，直接返回
        _isPlayerInput = true; // 玩家已经输入
        float currentBeatPoint = _beatPoints[0]; // 获取当前节拍点
        BeatResult _beatResult;
        if (Mathf.Abs(_scenceTime - currentBeatPoint) <= _maxmumTolerance)
        {
            _beatResult = BeatResult.Perfect; // 如果玩家的输入时机在节拍点附近
            PerfectBeat();
        }
        else if (_scenceTime < currentBeatPoint - _maxmumTolerance)
        {
            _beatResult = BeatResult.Early;
            EarlyBeat();
        }
        else if (_scenceTime > currentBeatPoint + _maxmumTolerance)
        {
            _beatResult = BeatResult.Late;
            LateBeat();
        }
        else
        {
            _beatResult = BeatResult.Miss;
            MissBeat();
        }
        _beatPoints.RemoveAt(0); // 移除第一个节拍点
        // 将玩家输入结果传递给EnemyController
        EnemyController.Instance.HandlePlayerInput(playerAction, _beatResult);
    }
    #endregion

    #region Beat Points Management
    private void MissBeat()
    {
        // 在这里实现错过节拍的逻辑
        PlayerActions.Instance.PerformMiss();
    }

    private void EarlyBeat()
    {
        // 在这里实现提前节拍的逻辑
        Debug.Log("Early beat at time: " + _scenceTime);
    }

    private void LateBeat()
    {
        // 在这里实现迟到节拍的逻辑
        Debug.Log("Late beat at time: " + _scenceTime);
    }

    private void PerfectBeat()
    {
        // 在这里实现完美节拍的逻辑
        Debug.Log("Perfect beat at time: " + _scenceTime);
    }
    #endregion
}
