using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PlayerActions
{
    // 创建一个事件PlayerAction，用于通知其他组件玩家执行了某个动作
    private static PlayerActions _instance; // 单例模式，确保每个场景只有一个控制器实例
    public delegate void PlayerInput(PlayerAction playerAction);
    public static event PlayerInput OnPlayerInput;
    private static PlayerAction _recentPlayerAction = PlayerAction.Empty;

    public static PlayerAction RecentPlayerAction
    {
        get { return _recentPlayerAction; }
    }

    private static bool _isInputLocked = false;

    public static PlayerActions Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new PlayerActions();
            }
            return _instance;
        }
    }

    //PlayerAction事件在OnYinButtonClicked和OnYangButtonClicked以及OnBothButtonsClicked时被触发
    //通过订阅OnYinButtonClicked和OnYangButtonClicked以及OnBothButtonsClicked事件来触发
    public static void SubscribeToYinYangEvents(YinYangController yinYangController)
    {
        if (yinYangController != null)
        {
            yinYangController.OnYinButtonClicked += () => TriggerPlayerInput(PlayerAction.Block);
            yinYangController.OnYangButtonClicked += () => TriggerPlayerInput(PlayerAction.Attack);
            yinYangController.OnBothButtonsClicked += () => TriggerPlayerInput(PlayerAction.Dodge);
        }
    }
    public static void TriggerPlayerInput(PlayerAction playerAction)
    {
        if (_isInputLocked) return;
        _recentPlayerAction = playerAction;
        LockInput();
        if (OnPlayerInput != null)
        {
            OnPlayerInput(playerAction);
        }
    }

    // 玩家执行攻击动作
    public static void PerformAttack()
    {
        TriggerPlayerInput(PlayerAction.Attack);
    }
    // 玩家执行防御动作
    public static void PerformDefend()
    {
        TriggerPlayerInput(PlayerAction.Block);
    }
    // 玩家执行躲避动作
    public static void PerformDodge()
    {
        TriggerPlayerInput(PlayerAction.Dodge);
    }
    // 玩家执行特殊动作
    public static void PerformSpecial()
    {
        TriggerPlayerInput(PlayerAction.Special);
    }

    public static void PerformMiss()
    {
        _recentPlayerAction = PlayerAction.Empty;
    }


    #region input system
    public static void LockInput()
    {
        //锁定输入
        _isInputLocked = true;
    }
    public static void UnlockInput()
    {
        //解锁输入
        _isInputLocked = false;
    }

    #endregion
}
