/target:library
/out:Temp/Unity.2D.Animation.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.2D.Animation.Editor.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.2D.Animation.Triangle.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.Common.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll
/reference:Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll
/reference:Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:"H:/Works/荡剑行歌/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
/reference:"H:/Works/荡剑行歌/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
/reference:"H:/Works/荡剑行歌/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/log4netPlastic.dll"
/reference:"H:/Works/荡剑行歌/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/unityplastic.dll"
/reference:"H:/Works/荡剑行歌/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_48
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\Analytics.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\AssemblyInfo.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\ClipperLib\clipper.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\ICharacterDataProvider.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\LayoutOverlay\DropdownMenu.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\LayoutOverlay\LayoutOverlay.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\LayoutOverlay\LayoutOverlayUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\LayoutOverlay\Manipulators\Draggable.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\LayoutOverlay\ScrollableToolbar.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\ResourceLoader.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\AssociateBonesScope.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\BaseTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\BoneDrawingUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Cache\BaseObject.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Cache\Cache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Cache\CacheObject.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\CharacterModeTool\SwitchModeTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\ColorExtensions.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\CopyTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\DefaultPoseScope.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\DrawingUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\GenerateGeometryTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\GenerateWeightsTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\HorizontalToggleTools.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\Brush.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\GUIWrapper.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\ISkeletonView.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\ISpriteMeshView.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\RectSelectionTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\RectSlider.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\SkeletonController.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\SkeletonView.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\Slider2D.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\SpriteMeshController.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\SpriteMeshView.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\UnselectTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IMGUI\WeightInspector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\IconUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\MathUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\MeshPreviewTool\IMeshPreviewBehaviour.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\MeshPreviewTool\MeshPreviewBehaviour.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\MeshPreviewTool\MeshPreviewTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\MeshTool\MeshTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\MeshTool\MeshToolWrapper.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\ModuleToolGroup.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\ModuleUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\OutlineGenerator\IOutlineGenerator.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\OutlineGenerator\OutlineGenerator.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selection\BoneSelection.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selection\IBoneSelection.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selection\ISelection.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selection\ITransformSelection.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selection\IndexedSelection.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selection\SerializableSelection.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selection\SkeletonSelection.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SelectionTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selectors\CircleVertexSelector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selectors\GenericVertexSelector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selectors\ICircleSelector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selectors\IRectSelector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selectors\ISelector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selectors\RectBoneSelector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selectors\RectVertexSelector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Selectors\Unselector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SerializableDictionary.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkeletonTool\SkeletonStyles.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkeletonTool\SkeletonTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkeletonTool\SkeletonToolView.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkeletonTool\SkeletonToolWrapper.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\BoneCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\BoneCacheExtensions.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\CharacterCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\CharacterPartCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\CharacterPartCacheExtensions.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\MeshCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\MeshPreviewCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\SkeletonCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\SkeletonCacheExtensions.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\SkinningCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\SkinningCachePersistentState.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\SkinningEnums.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\SkinningEvents.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\SpriteCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\SpriteCacheExtensions.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\TransformCache.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCache\TransformCacheExtensions.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningCopyUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningModule.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningModuleView.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningSerializer\ISkinningSerializer.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningSerializer\SkinningSerializerJSON.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SkinningSerializer\SkinningSerializerXML.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteBoneInfluence\SpriteBoneInfluenceListWidget.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteBoneInfluence\SpriteBoneInfluenceTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteBoneInfluence\SpriteBoneInfluenceWindow.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\BoneWeightExtensions.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\Edge.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\EditableBoneWeight.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\EditableBoneWeightUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\SmoothingUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\SpriteMeshData.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\SpriteMeshDataController.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\Vertex2D.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\SpriteMeshData\WeightEditor.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\TextContent.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Triangulation\ITriangulator.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Triangulation\TriangulationUtility.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Triangulation\Triangulator.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\BoneInspectorPanel.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\BoneToolbar.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\CopyToolbar.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\GenerateGeometryPanel.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\GenerateWeightsPanel.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\MeshToolbar.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\PastePanel.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\Toolbar.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\VisualElementExtensions.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\WeightInspectorIMGUIPanel.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\WeightPainterPanel.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UI\WeightToolbar.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Undo\DisableUndoScope.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Undo\DisabledUndo.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Undo\ICacheUndo.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Undo\IUndo.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Undo\UndoScope.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\Undo\UnityEngineUndo.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\UserSettings.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\BoneReparentTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\BoneTreeViewController.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\BoneTreeViewModel.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\BoneVisibilityTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\BoneVisibilityToolInterface.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\MeshVisibilityTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\SpriteVisibilityTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\VisibilityTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\VisibilityToolBase.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\VisibilityToolColumnHeader.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\VisibilityTool\VisibilityToolResizer.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\WeightPainterTool.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\WeightPainterToolWrapper.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\WeightsGenerator\BoundedBiharmonicWeightsGenerator.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SkinningModule\WeightsGenerator\IWeightsGenerator.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteLib\SpriteLibraryAssetInspector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteLib\SpriteLibraryDataProvider.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteLib\SpriteLibraryInspector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteLib\SpriteResolverInspector.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteLib\SpriteSelectionWidget.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteOutlineRenderer\SpriteOutlineRenderer.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpritePostProcess.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteSkin\BoneGizmo.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteSkin\BoneGizmoToggle.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteSkin\SpriteSkinCompositeDebugWindow.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteSkin\SpriteSkinEditor.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteSkin\SpriteSkinEntityEditor.cs"
"H:\Works\荡剑行歌\Library\PackageCache\com.unity.2d.animation@5.2.7\Editor\SpriteSkin\TransformExtensions.cs"
