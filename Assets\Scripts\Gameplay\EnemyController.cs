using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

public class EnemyController : MonoBehaviour
{

    private static EnemyController _instance; // 单例模式，确保每个场景只有一个控制器实例

    private EnemyState _currentState; // 当前敌人状态

    private bool _displayNextAction; // 是否展示下一次行动

    public Animator _animator; // 动画控制器引用

    public EnemyType _enemyType; // 敌人类型

    private List<EnemyAction> _enemyActions; // 敌人动作列表，用于存储敌人可以执行的动作
    public static EnemyController Instance
    {
        get { return _instance; }
    }

    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        _animator = GetComponent<Animator>(); // 获取动画控制器组件
        _currentState = EnemyState.Idle; // 初始化敌人状态
        _displayNextAction = true;
        loadMovements(_enemyType);
    }


    #region Enemy AI
    /// <summary>
    /// 敌人AI逻辑
    /// 根据玩家的输入和节拍结果决定敌人的行为
    /// 正常状态下，敌人的状态循环为 Idle -> PrepareAttack -> Attack -> AfterAttack -> Idle
    /// Idle, PrepareAttack时，敌人会从几种招式中随机选择一种，并展示
    /// 招式会持续数个节拍，在不同节拍上，会有攻击（玩家需要格挡），无法格挡攻击（玩家需要闪避），破绽(玩家可以攻击)三种行动
    /// 破绽行动可以被玩家攻击/防御, 在破绽时被攻击会削弱敌人韧性，韧性为0时敌人会进入DefenseLess状态
    /// DefenseLess状态下，敌人无法防御，玩家可以攻击敌人，造成额外伤害
    /// 玩家的输入包括攻击、格挡、闪避三种
    /// 格挡不会改变敌人的状态，但会影响敌人的攻击结果
    /// 攻击Idle和PrepareAttack状态下的敌人，敌人会立刻进入Attack状态，从几种招式中随机选择一种进行攻击并不展示
    /// </summary>
    public void HandlePlayerInput(PlayerAction playerAction, BeatResult beatResult)
    {
        //依据当前状态和玩家输入处理敌人行为
        switch (_currentState)
        {
            case EnemyState.Idle:
                if (playerAction == PlayerAction.Attack)
                {
                    // 玩家攻击，敌人进入Attack状态
                    _currentState = EnemyState.Attack;
                    EnterAttactState(showNextAction: false, nextFrameAction: EnemyAction.Block);
                }
                break;
            case EnemyState.PrepareAttack:
                if (playerAction == PlayerAction.Attack)
                {
                    // 玩家攻击，敌人进入Attack状态
                    _currentState = EnemyState.Attack;
                    EnterAttactState(showNextAction: false, nextFrameAction: EnemyAction.Block);
                }
                break;
            case EnemyState.Attack:
                //玩家的输入不会影响敌人
                break;
            case EnemyState.DefenseLess:
                //玩家的输入不会影响敌人
                break;
            case EnemyState.Defend:
                //玩家的输入不会影响敌人
                break;
        }

    }

    private void EnterAttactState(bool showNextAction, EnemyAction nextFrameAction)
    {
        _displayNextAction = showNextAction;

    }

    public EnemyAction GetNextAction()
    {
        if (_enemyActions.Count == 0) return EnemyAction.Idle;
        return _enemyActions[0];
    }
    #endregion
    #region Enemy Actions

    private void loadMovements(EnemyType enemyType)
    {
        //依据类型加载不同的动作模组

    }
    public void Attack()
    {
        // 实现敌人攻击逻辑
        _animator.SetTrigger("Attack");
        Debug.Log("Enemy attacks!");
    }

    public void Defend()
    {
        // 实现敌人防御逻辑
        _animator.SetTrigger("Defend");
        Debug.Log("Enemy defends!");
    }

    public void DefenseLess()
    {
        // 实现敌人无防御逻辑
        _animator.SetTrigger("DefenseLess");
        Debug.Log("Enemy is defense less!");
    }

    public void Defeated()
    {
        // 实现敌人被击败逻辑
        _animator.SetTrigger("Defeated");
        Debug.Log("Enemy defeated!");
    }
    #endregion
    
}
    