Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.48f1 (b805b124c6b7) revision 12060081'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
H:\Works\2020.3.48f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
H:/Works/荡剑行歌
-logFile
Logs/AssetImportWorker0.log
-srvPort
61292
Successfully changed project path to: H:/Works/荡剑行歌
H:/Works/荡剑行歌
Using Asset Import Pipeline V2.
Player connection [15720] Host "[IP] ************** [Port] 805523872 [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [15720] Host "[IP] ************** [Port] 805523872 [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 41.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1 (b805b124c6b7)
[Subsystems] Discovering subsystems at path H:/Works/2020.3.48f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/Works/荡剑行歌/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 SUPER (ID=0x2783)
    Vendor:   
    VRAM:     11999 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'H:/Works/2020.3.48f1/Editor/Data/Managed'
Mono path[1] = 'H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56324
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: H:/Works/2020.3.48f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.000989 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 48.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.610 seconds
Domain Reload Profiling:
	ReloadAssembly (610ms)
		BeginReloadAssembly (62ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (484ms)
			LoadAssemblies (62ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (149ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (23ms)
			SetupLoadedEditorAssemblies (201ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (49ms)
				BeforeProcessingInitializeOnLoad (12ms)
				ProcessInitializeOnLoadAttributes (104ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.004086 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 39.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.498 seconds
Domain Reload Profiling:
	ReloadAssembly (2500ms)
		BeginReloadAssembly (168ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (11ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (2286ms)
			LoadAssemblies (277ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (280ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (1630ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (39ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (1485ms)
				ProcessInitializeOnLoadMethodAttributes (14ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 1.02 seconds
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2842 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 122.3 MB.
System memory in use after: 122.5 MB.

Unloading 28 unused Assets to reduce memory usage. Loaded Objects now: 3282.
Total: 5.551800 ms (FindLiveObjects: 0.272800 ms CreateObjectMapping: 0.199900 ms MarkObjects: 4.921700 ms  DeleteObjects: 0.155300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Scripts/Gameplay/CombatController.cs
  artifactKey: Guid(9e10478b89fa78f4fb090319061af2ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Gameplay/CombatController.cs using Guid(9e10478b89fa78f4fb090319061af2ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ce2766876c387a49a5765ebc603c511f') in 0.049543 seconds 
