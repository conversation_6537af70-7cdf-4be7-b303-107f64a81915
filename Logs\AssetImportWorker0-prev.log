Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.48f1 (b805b124c6b7) revision 12060081'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
H:\Works\2020.3.48f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
H:/Works/荡剑行歌
-logFile
Logs/AssetImportWorker0.log
-srvPort
57243
Successfully changed project path to: H:/Works/荡剑行歌
H:/Works/荡剑行歌
Using Asset Import Pipeline V2.
Player connection [6120] Host "[IP] ************** [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [6120] Host "[IP] ************** [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 32.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1 (b805b124c6b7)
[Subsystems] Discovering subsystems at path H:/Works/2020.3.48f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/Works/荡剑行歌/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 SUPER (ID=0x2783)
    Vendor:   
    VRAM:     11999 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'H:/Works/2020.3.48f1/Editor/Data/Managed'
Mono path[1] = 'H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56424
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: H:/Works/2020.3.48f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.000807 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 35.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.418 seconds
Domain Reload Profiling:
	ReloadAssembly (418ms)
		BeginReloadAssembly (48ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (328ms)
			LoadAssemblies (48ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (92ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (16ms)
			SetupLoadedEditorAssemblies (146ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (35ms)
				BeforeProcessingInitializeOnLoad (18ms)
				ProcessInitializeOnLoadAttributes (70ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002076 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 35.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.906 seconds
Domain Reload Profiling:
	ReloadAssembly (906ms)
		BeginReloadAssembly (101ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (13ms)
		EndReloadAssembly (764ms)
			LoadAssemblies (82ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (196ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (396ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (36ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (295ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2842 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 122.3 MB.
System memory in use after: 122.5 MB.

Unloading 28 unused Assets to reduce memory usage. Loaded Objects now: 3282.
Total: 4.492100 ms (FindLiveObjects: 0.237000 ms CreateObjectMapping: 0.085800 ms MarkObjects: 4.077100 ms  DeleteObjects: 0.090700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Scripts/Factory/GameTypes.cs
  artifactKey: Guid(636a2523878188d44bb1614acfd27a00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Factory/GameTypes.cs using Guid(636a2523878188d44bb1614acfd27a00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '282dd3e955c45d56a6f0ec430ee2c463') in 0.023658 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002308 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.826 seconds
Domain Reload Profiling:
	ReloadAssembly (827ms)
		BeginReloadAssembly (97ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (696ms)
			LoadAssemblies (86ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (196ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (25ms)
			SetupLoadedEditorAssemblies (342ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (274ms)
				ProcessInitializeOnLoadMethodAttributes (9ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2818 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 120.7 MB.
System memory in use after: 120.9 MB.

Unloading 15 unused Assets to reduce memory usage. Loaded Objects now: 3285.
Total: 4.409300 ms (FindLiveObjects: 0.648600 ms CreateObjectMapping: 0.222600 ms MarkObjects: 3.504400 ms  DeleteObjects: 0.031500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001986 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.769 seconds
Domain Reload Profiling:
	ReloadAssembly (770ms)
		BeginReloadAssembly (92ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (642ms)
			LoadAssemblies (83ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (183ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (26ms)
			SetupLoadedEditorAssemblies (311ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (247ms)
				ProcessInitializeOnLoadMethodAttributes (7ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2818 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 120.7 MB.
System memory in use after: 120.9 MB.

Unloading 15 unused Assets to reduce memory usage. Loaded Objects now: 3288.
Total: 3.654500 ms (FindLiveObjects: 0.229800 ms CreateObjectMapping: 0.110400 ms MarkObjects: 3.281700 ms  DeleteObjects: 0.030800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
