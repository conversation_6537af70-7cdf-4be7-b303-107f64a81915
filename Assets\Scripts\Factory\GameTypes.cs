using System.Collections;
using System.Collections.Generic;
using UnityEngine;


/// <summary>
/// 全局游戏类型定义
/// 包含所有脚本都可能使用的枚举、结构体等数据类型
/// </summary>

// 节拍结果枚举
public enum BeatResult
{
    Perfect,    // 完美
    Early,      // 提前
    Late,       // 延迟
    Miss        // 失误
}

// 战斗动作类型
public enum PlayerAction
{
    Attack,     // 攻击
    Block,      // 格挡
    Dodge,      // 闪避
    Special,     // 特殊技能

    Empty // 无操作
}

//敌人类型
public enum EnemyType
{
    SwordsMan   // 剑士 
}
// 敌人状态
public enum EnemyState
{
    Idle,       // 空闲
    PrepareAttack, // 攻击前摇

    Attack, // 攻击

    Defend,     // 防御

    DefenseLess, // 无防御状态
}

// 敌人动作
public enum EnemyAction
{
    Idle,     // 空闲
    Attack,     // 攻击
    Block,      // 格挡

    Exposed,     // 破绽

    Stunned,     // 虚弱状态
}

// 游戏状态
public enum GameState
{
    Menu,       // 菜单
    Playing,    // 游戏中
    Paused,     // 暂停
    GameOver    // 游戏结束
}


