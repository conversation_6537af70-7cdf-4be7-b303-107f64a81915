[api-updater (non-obsolete-error-filter)] 2025/6/3 21:16:47 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 163.4869ms
moved types parse time: 37ms
candidates parse time : 1ms
C# parse time         : 206ms
candidates check time : 41ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/3 21:19:41 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 56.7695ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 123ms
candidates check time : 31ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/3 21:23:08 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 53.2229ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 102ms
candidates check time : 25ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/3 21:24:59 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 54.8027ms
moved types parse time: 33ms
candidates parse time : 0ms
C# parse time         : 101ms
candidates check time : 25ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/3 21:26:26 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 64.5261ms
moved types parse time: 35ms
candidates parse time : 1ms
C# parse time         : 103ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/3 21:27:11 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 73.0821ms
moved types parse time: 36ms
candidates parse time : 0ms
C# parse time         : 105ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/3 21:32:51 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 72.102ms
moved types parse time: 36ms
candidates parse time : 1ms
C# parse time         : 136ms
candidates check time : 30ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/30 21:37:05 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 204.5181ms
moved types parse time: 40ms
candidates parse time : 0ms
C# parse time         : 246ms
candidates check time : 28ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/30 21:37:28 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 51.9878ms
moved types parse time: 36ms
candidates parse time : 0ms
C# parse time         : 122ms
candidates check time : 26ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/30 21:37:32 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 60.4183ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 133ms
candidates check time : 32ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/6/30 21:37:40 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 70.128ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 119ms
candidates check time : 28ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/24 21:55:18 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 194.5489ms
moved types parse time: 39ms
candidates parse time : 0ms
C# parse time         : 240ms
candidates check time : 19ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/24 22:02:13 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 58.3188ms
moved types parse time: 35ms
candidates parse time : 1ms
C# parse time         : 119ms
candidates check time : 19ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/24 22:05:02 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 72.1537ms
moved types parse time: 44ms
candidates parse time : 0ms
C# parse time         : 143ms
candidates check time : 33ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/24 22:07:03 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 57.8195ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 113ms
candidates check time : 19ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/29 22:08:28 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 185.9638ms
moved types parse time: 39ms
candidates parse time : 0ms
C# parse time         : 265ms
candidates check time : 19ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/29 22:09:40 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 73.3951ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 110ms
candidates check time : 15ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/29 22:09:49 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 82.3738ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 129ms
candidates check time : 18ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/29 22:10:04 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 67.9384ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 127ms
candidates check time : 17ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/7/30 21:30:34 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 242.0064ms
moved types parse time: 39ms
candidates parse time : 0ms
C# parse time         : 212ms
candidates check time : 19ms
console write time    : 0ms

